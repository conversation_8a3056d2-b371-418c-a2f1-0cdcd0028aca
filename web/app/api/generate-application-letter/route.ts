export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { generateAIApplicationLetter } from '@/utils/ai-generators/applicationLetterGenerator';
import { generatePlainTextLetter, prepareStreamingData } from '@/utils/ai-generators/applicationLetterGeneratorStreaming';
import { generateStructuredLetterData } from '@/utils/ai-generators/structuredLetterGenerator';
import { createApiHandler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { trackApiUsage } from '@/lib/mixpanel-server';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';
import { fillLetterTemplate } from '@/utils/letter-template-engine';
import { convertToLetterTemplateData, validateStructuredLetterData } from '@/types/letter-structured';

async function handleRequest(request: NextRequest, context?: { params: Record<string, string | string[]> }) {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;
  
  
  let formData = new FormData();
  try {
    formData = await request.formData();
    
    const jobDescription = formData.get('jobDescription') as string | null;
    const jobImage = formData.get('jobImage') as File | null;
    const unauthenticatedResumeFile = formData.get('unauthenticatedResumeFile') as File | null;
    const unauthenticatedResumeFileName = formData.get('unauthenticatedResumeFileName') as string | null;
    const templateId = formData.get('templateId') as string | 'plain-text';
    const editedLetterText = formData.get('editedLetterText') as string | null;
    const existingLetterId = formData.get('existingLetterId') as string | null;
    
    if (!jobDescription && !jobImage) {
      return NextResponse.json({
        error: 'Deskripsi pekerjaan dan gambar lowongan diperlukan' 
      }, { status: 400 });
    }
    
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    let buffer: ArrayBuffer;
    let fileName: string;

    if (unauthenticatedResumeFile && unauthenticatedResumeFileName) {
      buffer = await unauthenticatedResumeFile.arrayBuffer();
      fileName = unauthenticatedResumeFileName.toLowerCase();
    } else if (accessToken) {
      // Create Supabase client
      const supabase = await createClient();
          
      // Get user with the provided token
      const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

      if (userError || !user) {
        console.error('Error getting user with token:', userError);
        return NextResponse.json({ 
          error: 'Anda harus login untuk menggunakan fitur ini' 
        }, { status: 401 });
      }
      userId = user.id;

      // Get resume data from Supabase storage
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('resume_file_name, tokens')
        .eq('id', userId)
        .single();

      if (fetchError || !profile) {
        return NextResponse.json({ 
          error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.' 
        }, { status: 404 });
      }

      // Check token balance against template cost (skip for edit operations)
      const selectedTemplate = getTemplateById(templateId);
      const requiredTokens = selectedTemplate?.tokenCost ?? 0;
      const currentTokens = (profile.tokens as number | null) ?? 0;

      // Only check token balance for new generations, not for edits
      if (!editedLetterText && requiredTokens > 0 && currentTokens < requiredTokens) {
        return NextResponse.json({
          error: 'Token Anda tidak cukup untuk menggunakan template ini. Silakan top up token Anda.'
        }, { status: 402 });
      }

      // Get the resume file from storage
      const { data: resumeFile, error: storageError } = await supabase.storage
        .from('resumes')
        .download(profile.resume_file_name);

      if (storageError || !resumeFile) {
        captureApiError('generate-application-letter', storageError || 'Resume file access error', {
          userId,
          type: 'resume_storage_access',
          resumeFileName: profile.resume_file_name
        });
        return NextResponse.json({ 
          error: 'Tidak dapat mengakses file resume. Harap unggah ulang resume Anda.' 
        }, { status: 500 });
      }

      // Convert the file to buffer and determine correct mime type
      buffer = await resumeFile.arrayBuffer();
      fileName = profile.resume_file_name.toLowerCase();
    } else {
      return NextResponse.json({ 
        error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.' 
      }, { status: 404 });
    }
    
    // Get the correct mime type based on file extension
    let mimeType: string;
    if (fileName.endsWith('.pdf')) {
      mimeType = 'application/pdf';
    } else if (fileName.endsWith('.docx')) {
      mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (fileName.endsWith('.png')) {
      mimeType = 'image/png';
    } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
      mimeType = 'image/jpeg';
    } else {
      mimeType = 'text/plain';
    }
    
    // Prepare job image if it exists
    let jobImageData: { buffer: ArrayBuffer, mimeType: string } | undefined;
    if (jobImage) {
      const jobImageBuffer = await jobImage.arrayBuffer();
      jobImageData = { 
        buffer: jobImageBuffer, 
        mimeType: jobImage.type 
      };
    }

    // Check which generation mode to use
    const useStreaming = formData.get('useStreaming') === 'true';
    const useUnifiedStreaming = formData.get('useUnifiedStreaming') === 'true';
    const useStructuredGeneration = formData.get('useStructuredGeneration') === 'true';
    
    if (useStructuredGeneration) {
      // New structured data generation approach
      try {
        // Get selected template
        const selectedTemplate = getTemplateById(templateId);
        if (!selectedTemplate) {
          return NextResponse.json({
            error: 'Template tidak ditemukan'
          }, { status: 404 });
        }

        // Generate structured data using AI
        const structuredData = await generateStructuredLetterData(
          { buffer, mimeType },
          jobDescription || undefined,
          jobImageData,
          {
            templateId,
            language: 'id', // Default to Indonesian
            extractCompanyFromJob: true,
            includeAttachments: true
          }
        );

        // Validate structured data
        const validation = validateStructuredLetterData(structuredData);
        if (!validation.isValid) {
          console.warn('Generated structured data validation failed:', validation.missingFields);
          // Continue with warnings - the fillLetterTemplate will handle missing fields
        }

        let finalHtml = '';
        
        try {
          finalHtml = fillLetterTemplate(selectedTemplate, structuredData);
        } catch (templateError) {
          console.error('Template rendering error:', templateError);
          return NextResponse.json({
            error: 'Gagal membuat desain surat. Silakan coba template lain.'
          }, { status: 500 });
        }

        // Save to database if user is authenticated
        let letterId: string | undefined;
        if (userId) {
          try {
            const supabase = await createClient();

            // Deduct tokens for template usage
            if (selectedTemplate.tokenCost && selectedTemplate.tokenCost > 0) {
              // First get current token balance
              const { data: profile } = await supabase
                .from('profiles')
                .select('tokens')
                .eq('id', userId)
                .single();

              if (profile && profile.tokens >= selectedTemplate.tokenCost) {
                const { error: tokenError } = await supabase
                  .from('profiles')
                  .update({
                    tokens: profile.tokens - selectedTemplate.tokenCost
                  })
                  .eq('id', userId);

                if (tokenError) {
                  console.error('Error deducting tokens:', tokenError);
                  // Continue anyway - don't fail the generation for token errors
                }
              }
            }

            // Save letter to database
            const letterData = {
              user_id: userId,
              structured_data: structuredData,
              design_html: finalHtml || null,
              template_id: templateId,
              plain_text: null // We don't generate plain text in structured mode
            };

            if (existingLetterId && editedLetterText) {
              // Update existing letter
              const { data: savedLetter, error: saveError } = await supabase
                .from('letters')
                .update(letterData)
                .eq('id', existingLetterId)
                .eq('user_id', userId)
                .select('id')
                .single();

              if (saveError) {
                console.error('Error updating letter:', saveError);
              } else {
                letterId = savedLetter?.id;
              }
            } else {
              // Create new letter
              const { data: savedLetter, error: saveError } = await supabase
                .from('letters')
                .insert(letterData)
                .select('id')
                .single();

              if (saveError) {
                console.error('Error saving letter:', saveError);
              } else {
                letterId = savedLetter?.id;
              }
            }
          } catch (dbError) {
            console.error('Database error:', dbError);
            // Continue - don't fail generation for DB errors
          }
        }

        // Calculate request duration
        const duration = Date.now() - startTime;

        // Track successful API usage
        trackApiUsage(
          'generate-application-letter-structured',
          'success',
          duration,
          {
            has_job_description: !!jobDescription,
            has_job_image: !!jobImage,
            is_authenticated: !!userId,
            resume_type: fileName.split('.').pop(),
            template_id: templateId,
            has_structured_data: true
          },
          userId
        );

        return NextResponse.json({
          success: true,
          data: {
            structuredData,
            design: finalHtml || null,
            templateId,
            letterId,
            generationMode: 'structured'
          }
        });

      } catch (error) {
        console.error('Error in structured generation:', error);
        
        // Calculate request duration even for errors
        const duration = Date.now() - startTime;
        
        // Track failed API usage
        trackApiUsage(
          'generate-application-letter-structured',
          'error',
          duration,
          {
            error_message: error instanceof Error ? error.message : 'Unknown error',
            is_authenticated: !!userId,
            template_id: templateId
          },
          userId
        );

        return NextResponse.json({
          success: false,
          error: 'Gagal membuat surat lamaran dengan data terstruktur. Silakan coba lagi.'
        }, { status: 500 });
      }
      
    } else if (useUnifiedStreaming) {
      // Prepare data for unified streaming (structured data generation)
      
      // Convert resume file to appropriate format for edge function
      let resumeData: string;
      let resumeMimeType: string = mimeType;
      
      if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        // Extract text from DOCX for edge function
        const { extractTextFromDocx } = await import('@/utils/docxUtils');
        resumeData = await extractTextFromDocx(buffer);
        resumeMimeType = 'text/plain';
      } else if (mimeType === 'text/plain') {
        resumeData = Buffer.from(buffer).toString('utf-8');
      } else {
        // For PDF and images, convert to base64
        resumeData = Buffer.from(buffer).toString('base64');
      }
      
      // Convert job image to base64 if provided
      let jobImageBase64: string | undefined;
      if (jobImageData) {
        jobImageBase64 = Buffer.from(jobImageData.buffer).toString('base64');
      }
      
      // Get template information
      const selectedTemplate = getTemplateById(templateId);
      
      // Calculate request duration for preparation
      const duration = Date.now() - startTime;
      
      // Track successful API usage in Mixpanel
      trackApiUsage(
        'generate-application-letter-unified-streaming',
        'success',
        duration,
        {
          has_job_description: !!jobDescription,
          has_job_image: !!jobImage,
          is_authenticated: !!userId,
          resume_type: fileName.split('.').pop(),
          template_id: templateId,
          phase: 'preparation',
          generation_mode: 'structured'
        },
        userId
      );
      
      // Return unified streaming information for client with structured data generation
      return NextResponse.json({
        success: true,
        data: {
          streaming: {
            enabled: true,
            unified: true,
            structured: true, // Flag to indicate structured data generation
            endpoint: '/api/edge/generate-letter-unified',
            data: {
              resumeData,
              resumeMimeType,
              jobDescription: jobDescription || undefined,
              jobImage: jobImageBase64,
              templateId,
              templateHtml: selectedTemplate?.templateHtml, // Keep for backward compatibility
              userId,
              letterId: existingLetterId || undefined,
              isEdit: !!editedLetterText,
              useStructuredGeneration: true // Signal to edge function
            }
          },
          price: selectedTemplate?.tokenCost ? 15000 : undefined,
          currency: selectedTemplate?.tokenCost ? 'IDR' : undefined,
          generationMode: 'structured-streaming'
        }
      });
    } else if (useStreaming) {
      // DEPRECATED: Legacy streaming approach (kept for backward compatibility)
      // TODO: Remove this path once all clients are migrated to unified streaming
      // Phase 1: Generate plain text letter using Gemini (fast)
      const plainText = await generatePlainTextLetter(
        { buffer, mimeType },
        jobDescription || undefined,
        jobImageData,
        editedLetterText || undefined
      );
      
      // Phase 2: Prepare streaming data
      const streamingData = await prepareStreamingData(
        plainText,
        templateId,
        userId,
        !!editedLetterText, // isEdit = true if editedLetterText is provided
        existingLetterId || undefined // Pass existing letterId for updates
      );
      
      // Calculate request duration for phase 1
      const duration = Date.now() - startTime;
      
      // Track successful API usage in Mixpanel
      trackApiUsage(
        'generate-application-letter-streaming-legacy',
        'success',
        duration,
        {
          has_job_description: !!jobDescription,
          has_job_image: !!jobImage,
          is_authenticated: !!userId,
          resume_type: fileName.split('.').pop(),
          template_id: templateId,
          phase: 'plain-text',
          deprecated: true
        },
        userId
      );
      
      // Return streaming information for client to continue with Edge Function
      return NextResponse.json({
        success: true,
        data: {
          plainText: streamingData.plainText,
          letterId: streamingData.letterId,
          price: streamingData.price,
          currency: streamingData.currency,
          streaming: {
            enabled: true,
            endpoint: '/api/edge/generate-application-letter',
            templateHtml: streamingData.templateHtml,
            templateId: streamingData.templateId,
            isEdit: streamingData.isEdit
          }
        }
      });
    } else {
      // DEPRECATED: Original non-streaming approach (kept for backward compatibility)
      // TODO: Remove this path once all clients are migrated to structured generation
      const applicationLetterData = await generateAIApplicationLetter(
        { buffer, mimeType },
        userId,
        jobDescription || undefined,
        jobImageData,
        templateId,
        editedLetterText || undefined
      );
      
      // Calculate request duration
      const duration = Date.now() - startTime;
      
      // Track successful API usage in Mixpanel
      trackApiUsage(
        'generate-application-letter-legacy',
        'success',
        duration,
        {
          has_job_description: !!jobDescription,
          has_job_image: !!jobImage,
          is_authenticated: !!userId,
          resume_type: fileName.split('.').pop(),
          has_designs: !!applicationLetterData.design,
          deprecated: true
        },
        userId
      );
      
      return NextResponse.json({
        success: true,
        data: applicationLetterData
      });
    }
    
  } catch (error) {
    console.error('Error generating application letter:', error);
    
    // Calculate request duration even for errors
    const duration = Date.now() - startTime;
    
    // Track failed API usage in Mixpanel
    trackApiUsage(
      'generate-application-letter', 
      'error',
      duration,
      {
        error_message: error instanceof Error ? error.message : 'Unknown error',
        is_authenticated: !!userId
      },
      userId
    );
    
    // Capture error details with Rollbar
    captureApiError('generate-application-letter', error, {
      requestUrl: request.url
    });
    
    return NextResponse.json({
      success: false,
      error: 'Gagal membuat surat lamaran. Silakan coba lagi.'
    }, { status: 500 });
  }
}

// Export the handler with error reporting wrapper
export const POST = createApiHandler('generate-application-letter', handleRequest);
